package com.agentic.framework.mcp.tools;

import com.agentic.framework.mcp.protocol.McpProtocol;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * Registry for managing MCP tools
 */
@Component
public class ToolRegistry {
    
    private static final Logger logger = LoggerFactory.getLogger(ToolRegistry.class);
    
    private final Map<String, McpTool> tools = new ConcurrentHashMap<>();
    private final List<McpTool> toolBeans;
    
    @Autowired
    public ToolRegistry(List<McpTool> toolBeans) {
        this.toolBeans = toolBeans;
    }
    
    @PostConstruct
    public void initializeTools() {
        // Register all tool beans
        for (McpTool tool : toolBeans) {
            registerTool(tool);
        }
        
        logger.info("Registered {} MCP tools", tools.size());
    }
    
    /**
     * Register a tool
     */
    public void registerTool(McpTool tool) {
        String name = tool.getName();
        if (tools.containsKey(name)) {
            logger.warn("Tool already registered: {}, replacing", name);
        }
        tools.put(name, tool);
        logger.debug("Registered tool: {}", name);
    }
    
    /**
     * Get a tool by name
     */
    public McpTool getTool(String name) {
        return tools.get(name);
    }
    
    /**
     * Check if a tool exists
     */
    public boolean hasTool(String name) {
        return tools.containsKey(name);
    }
    
    /**
     * Get all available tools
     */
    public Collection<McpTool> getAllTools() {
        return Collections.unmodifiableCollection(tools.values());
    }
    
    /**
     * Get tool definitions for MCP protocol
     */
    public List<McpProtocol.Tool> getToolDefinitions() {
        return tools.values().stream()
            .filter(McpTool::isAvailable)
            .map(tool -> {
                McpProtocol.Tool definition = new McpProtocol.Tool();
                definition.setName(tool.getName());
                definition.setDescription(tool.getDescription());
                definition.setInputSchema(tool.getInputSchema());
                return definition;
            })
            .collect(Collectors.toList());
    }
    
    /**
     * Get available tool names
     */
    public Set<String> getAvailableToolNames() {
        return tools.entrySet().stream()
            .filter(entry -> entry.getValue().isAvailable())
            .map(Map.Entry::getKey)
            .collect(Collectors.toSet());
    }
}