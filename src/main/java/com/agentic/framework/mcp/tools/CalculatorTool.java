package com.agentic.framework.mcp.tools;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Component;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * MCP Tool for performing mathematical calculations
 */
@Component
public class CalculatorTool implements McpTool {
    
    private final ScriptEngine scriptEngine;
    private final ObjectMapper objectMapper;
    
    public CalculatorTool() {
        ScriptEngineManager manager = new ScriptEngineManager();
        this.scriptEngine = manager.getEngineByName("JavaScript");
        this.objectMapper = new ObjectMapper();
    }
    
    @Override
    public String getName() {
        return "calculator";
    }
    
    @Override
    public String getDescription() {
        return "Perform mathematical calculations and evaluations";
    }
    
    @Override
    public JsonNode getInputSchema() {
        Map<String, Object> schema = new HashMap<>();
        schema.put("type", "object");
        
        Map<String, Object> properties = new HashMap<>();
        properties.put("expression", Map.of(
            "type", "string",
            "description", "Mathematical expression to evaluate (e.g., '2 + 2', 'Math.sqrt(16)')"
        ));
        
        schema.put("properties", properties);
        schema.put("required", new String[]{"expression"});
        
        return objectMapper.valueToTree(schema);
    }
    
    @Override
    public CompletableFuture<Object> execute(Map<String, Object> arguments, String userId, String sessionId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                String expression = (String) arguments.get("expression");
                
                // Sanitize the expression to prevent code injection
                expression = expression.replaceAll("[^0-9+\\-*/().\\s,Math\\w]", "");
                
                Object result = scriptEngine.eval(expression);
                
                return Map.of(
                    "success", true,
                    "expression", expression,
                    "result", result != null ? result.toString() : "null"
                );
            } catch (Exception e) {
                return Map.of(
                    "success", false,
                    "error", "Failed to evaluate expression: " + e.getMessage()
                );
            }
        });
    }
}