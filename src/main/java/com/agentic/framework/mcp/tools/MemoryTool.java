package com.agentic.framework.mcp.tools;

import com.agentic.framework.mcp.ContextManager;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * MCP Tool for managing user memories and context
 */
@Component
public class MemoryTool implements McpTool {
    
    private final ContextManager contextManager;
    private final ObjectMapper objectMapper;
    
    @Autowired
    public MemoryTool(ContextManager contextManager, ObjectMapper objectMapper) {
        this.contextManager = contextManager;
        this.objectMapper = objectMapper;
    }
    
    @Override
    public String getName() {
        return "memory_management";
    }
    
    @Override
    public String getDescription() {
        return "Store, retrieve, and search through conversation memories and context";
    }
    
    @Override
    public JsonNode getInputSchema() {
        Map<String, Object> schema = new HashMap<>();
        schema.put("type", "object");
        
        Map<String, Object> properties = new HashMap<>();
        
        properties.put("action", Map.of(
            "type", "string",
            "enum", new String[]{"store", "retrieve", "search", "clear"},
            "description", "The memory operation to perform"
        ));
        
        properties.put("query", Map.of(
            "type", "string",
            "description", "Search query for finding specific memories"
        ));
        
        properties.put("limit", Map.of(
            "type", "integer",
            "description", "Maximum number of memories to retrieve",
            "default", 10
        ));
        
        properties.put("content", Map.of(
            "type", "string",
            "description", "Content to store in memory"
        ));
        
        schema.put("properties", properties);
        schema.put("required", new String[]{"action"});
        
        return objectMapper.valueToTree(schema);
    }
    
    @Override
    public CompletableFuture<Object> execute(Map<String, Object> arguments, String userId, String sessionId) {
        return CompletableFuture.supplyAsync(() -> {
            String action = (String) arguments.get("action");
            
            switch (action) {
                case "retrieve":
                    int limit = arguments.containsKey("limit") ? 
                        ((Number) arguments.get("limit")).intValue() : 10;
                    return Map.of(
                        "success", true,
                        "memories", contextManager.getContext(userId, sessionId, limit),
                        "count", contextManager.getUserContextCount(userId)
                    );
                    
                case "search":
                    String query = (String) arguments.get("query");
                    int searchLimit = arguments.containsKey("limit") ? 
                        ((Number) arguments.get("limit")).intValue() : 10;
                    return Map.of(
                        "success", true,
                        "results", contextManager.getMemories(userId, query, searchLimit)
                    );
                    
                case "clear":
                    if (sessionId != null) {
                        contextManager.clearContext(userId, sessionId);
                    } else {
                        contextManager.clearUserData(userId);
                    }
                    return Map.of(
                        "success", true,
                        "message", "Memory cleared successfully"
                    );
                    
                case "store":
                    // This is handled automatically when processing requests
                    return Map.of(
                        "success", true,
                        "message", "Memory storage is automatic"
                    );
                    
                default:
                    return Map.of(
                        "success", false,
                        "error", "Unknown action: " + action
                    );
            }
        });
    }
}