package com.agentic.framework.mcp.tools;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * MCP Tool for file system operations
 */
@Component
public class FileSystemTool implements McpTool {
    
    private final ObjectMapper objectMapper;
    private final Path baseDirectory;
    
    public FileSystemTool(@Value("${mcp.tools.filesystem.base-dir:./data}") String baseDir) {
        this.objectMapper = new ObjectMapper();
        this.baseDirectory = Paths.get(baseDir).toAbsolutePath();
        
        // Create base directory if it doesn't exist
        try {
            Files.createDirectories(this.baseDirectory);
        } catch (IOException e) {
            throw new RuntimeException("Failed to create base directory: " + baseDir, e);
        }
    }
    
    @Override
    public String getName() {
        return "filesystem";
    }
    
    @Override
    public String getDescription() {
        return "Read, write, and manage files in the file system";
    }
    
    @Override
    public JsonNode getInputSchema() {
        Map<String, Object> schema = new HashMap<>();
        schema.put("type", "object");
        
        Map<String, Object> properties = new HashMap<>();
        
        properties.put("operation", Map.of(
            "type", "string",
            "enum", new String[]{"read", "write", "list", "delete", "exists", "mkdir"},
            "description", "File system operation to perform"
        ));
        
        properties.put("path", Map.of(
            "type", "string",
            "description", "Relative path to the file or directory"
        ));
        
        properties.put("content", Map.of(
            "type", "string",
            "description", "Content to write to the file (for write operation)"
        ));
        
        properties.put("recursive", Map.of(
            "type", "boolean",
            "description", "Whether to perform operation recursively",
            "default", false
        ));
        
        schema.put("properties", properties);
        schema.put("required", new String[]{"operation", "path"});
        
        return objectMapper.valueToTree(schema);
    }
    
    @Override
    public CompletableFuture<Object> execute(Map<String, Object> arguments, String userId, String sessionId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                String operation = (String) arguments.get("operation");
                String pathStr = (String) arguments.get("path");
                
                // Validate and resolve path
                Path targetPath = validateAndResolvePath(pathStr, userId);
                
                switch (operation) {
                    case "read":
                        return readFile(targetPath);
                        
                    case "write":
                        String content = (String) arguments.get("content");
                        return writeFile(targetPath, content);
                        
                    case "list":
                        return listDirectory(targetPath);
                        
                    case "delete":
                        boolean recursive = Boolean.TRUE.equals(arguments.get("recursive"));
                        return deleteFile(targetPath, recursive);
                        
                    case "exists":
                        return checkExists(targetPath);
                        
                    case "mkdir":
                        return createDirectory(targetPath);
                        
                    default:
                        return Map.of(
                            "success", false,
                            "error", "Unknown operation: " + operation
                        );
                }
            } catch (Exception e) {
                return Map.of(
                    "success", false,
                    "error", e.getMessage()
                );
            }
        });
    }
    
    private Path validateAndResolvePath(String pathStr, String userId) throws IOException {
        // Create user-specific directory
        Path userDir = baseDirectory.resolve(userId);
        Files.createDirectories(userDir);
        
        // Resolve and normalize the path
        Path resolved = userDir.resolve(pathStr).normalize();
        
        // Ensure the path is within the user's directory
        if (!resolved.startsWith(userDir)) {
            throw new SecurityException("Access denied: Path is outside user directory");
        }
        
        return resolved;
    }
    
    private Map<String, Object> readFile(Path path) {
        try {
            if (!Files.exists(path)) {
                return Map.of(
                    "success", false,
                    "error", "File not found: " + path.getFileName()
                );
            }
            
            String content = Files.readString(path);
            return Map.of(
                "success", true,
                "content", content,
                "size", Files.size(path),
                "lastModified", Files.getLastModifiedTime(path).toString()
            );
        } catch (IOException e) {
            return Map.of(
                "success", false,
                "error", "Failed to read file: " + e.getMessage()
            );
        }
    }
    
    private Map<String, Object> writeFile(Path path, String content) {
        try {
            Files.createDirectories(path.getParent());
            Files.writeString(path, content, StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
            
            return Map.of(
                "success", true,
                "message", "File written successfully",
                "size", Files.size(path)
            );
        } catch (IOException e) {
            return Map.of(
                "success", false,
                "error", "Failed to write file: " + e.getMessage()
            );
        }
    }
    
    private Map<String, Object> listDirectory(Path path) {
        try {
            if (!Files.exists(path)) {
                Files.createDirectories(path);
            }
            
            if (!Files.isDirectory(path)) {
                return Map.of(
                    "success", false,
                    "error", "Path is not a directory"
                );
            }
            
            List<Map<String, Object>> files;
            try (Stream<Path> stream = Files.list(path)) {
                files = stream.map(p -> {
                    Map<String, Object> fileInfo = new HashMap<>();
                    fileInfo.put("name", p.getFileName().toString());
                    fileInfo.put("isDirectory", Files.isDirectory(p));
                    try {
                        fileInfo.put("size", Files.size(p));
                        fileInfo.put("lastModified", Files.getLastModifiedTime(p).toString());
                    } catch (IOException e) {
                        // Ignore errors for individual files
                    }
                    return fileInfo;
                }).collect(Collectors.toList());
            }
            
            return Map.of(
                "success", true,
                "files", files,
                "count", files.size()
            );
        } catch (IOException e) {
            return Map.of(
                "success", false,
                "error", "Failed to list directory: " + e.getMessage()
            );
        }
    }
    
    private Map<String, Object> deleteFile(Path path, boolean recursive) {
        try {
            if (!Files.exists(path)) {
                return Map.of(
                    "success", false,
                    "error", "File not found"
                );
            }
            
            if (recursive && Files.isDirectory(path)) {
                Files.walkFileTree(path, new SimpleFileVisitor<Path>() {
                    @Override
                    public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                        Files.delete(file);
                        return FileVisitResult.CONTINUE;
                    }
                    
                    @Override
                    public FileVisitResult postVisitDirectory(Path dir, IOException exc) throws IOException {
                        Files.delete(dir);
                        return FileVisitResult.CONTINUE;
                    }
                });
            } else {
                Files.delete(path);
            }
            
            return Map.of(
                "success", true,
                "message", "File deleted successfully"
            );
        } catch (IOException e) {
            return Map.of(
                "success", false,
                "error", "Failed to delete file: " + e.getMessage()
            );
        }
    }
    
    private Map<String, Object> checkExists(Path path) {
        boolean exists = Files.exists(path);
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("exists", exists);
        
        if (exists) {
            result.put("isDirectory", Files.isDirectory(path));
            result.put("isFile", Files.isRegularFile(path));
            try {
                result.put("size", Files.size(path));
                result.put("lastModified", Files.getLastModifiedTime(path).toString());
            } catch (IOException e) {
                // Ignore
            }
        }
        
        return result;
    }
    
    private Map<String, Object> createDirectory(Path path) {
        try {
            Files.createDirectories(path);
            return Map.of(
                "success", true,
                "message", "Directory created successfully"
            );
        } catch (IOException e) {
            return Map.of(
                "success", false,
                "error", "Failed to create directory: " + e.getMessage()
            );
        }
    }
}