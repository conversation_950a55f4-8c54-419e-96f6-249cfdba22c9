package com.agentic.framework.mcp.tools;

import com.fasterxml.jackson.databind.JsonNode;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Base interface for MCP Tools
 */
public interface McpTool {
    
    /**
     * Get the name of the tool
     */
    String getName();
    
    /**
     * Get the description of the tool
     */
    String getDescription();
    
    /**
     * Get the JSON schema for the tool's input parameters
     */
    JsonNode getInputSchema();
    
    /**
     * Execute the tool with the given arguments
     */
    CompletableFuture<Object> execute(Map<String, Object> arguments, String userId, String sessionId);
    
    /**
     * Check if the tool is currently available
     */
    default boolean isAvailable() {
        return true;
    }
}