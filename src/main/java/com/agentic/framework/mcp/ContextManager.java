package com.agentic.framework.mcp;

import com.agentic.framework.model.ModelRequest;
import com.agentic.framework.model.ModelResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import jakarta.annotation.PostConstruct;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Manages user context and memory persistence within the MCP server
 * Provides in-memory storage with optional file-based persistence
 */
@Component
public class ContextManager {
    
    private static final Logger logger = LoggerFactory.getLogger(ContextManager.class);
    
    private final ConcurrentMap<String, UserContext> userContexts = new ConcurrentHashMap<>();
    private final ConcurrentMap<String, UserMemory> userMemories = new ConcurrentHashMap<>();
    private final ScheduledExecutorService cleanupExecutor;
    
    @Value("${context.storage.max-contexts-per-user:100}")
    private int maxContextsPerUser;
    
    @Value("${context.storage.max-context-size:10000}")
    private int maxContextSize;
    
    @Value("${context.storage.cleanup-interval:3600000}")
    private long cleanupInterval;
    
    @Value("${context.memory.max-memories-per-user:1000}")
    private int maxMemoriesPerUser;
    
    @Value("${context.memory.memory-ttl:86400000}")
    private long memoryTtl;
    
    public ContextManager() {
        this.cleanupExecutor = Executors.newSingleThreadScheduledExecutor();
    }

    @PostConstruct
    private void startCleanupTask() {
        cleanupExecutor.scheduleAtFixedRate(
            this::performCleanup,
            cleanupInterval,
            cleanupInterval,
            TimeUnit.MILLISECONDS
        );
        logger.info("Context cleanup task started with interval: {} ms", cleanupInterval);
    }
    
    public void addContext(String userId, String sessionId, ModelRequest request, ModelResponse response) {
        UserContext context = userContexts.computeIfAbsent(userId, k -> new UserContext(userId));
        context.addInteraction(sessionId, request, response);
        
        // Add to memory
        UserMemory memory = userMemories.computeIfAbsent(userId, k -> new UserMemory(userId));
        memory.addMemory(request, response);
        
        logger.debug("Added context for user: {}, session: {}", userId, sessionId);
    }
    
    public List<ContextInteraction> getContext(String userId, String sessionId, int limit) {
        UserContext context = userContexts.get(userId);
        if (context == null) {
            return Collections.emptyList();
        }
        
        List<ContextInteraction> interactions = context.getInteractions(sessionId);
        if (interactions.size() <= limit) {
            return new ArrayList<>(interactions);
        }
        
        // Return last N interactions
        return new ArrayList<>(interactions.subList(interactions.size() - limit, interactions.size()));
    }
    
    public List<MemoryEntry> getMemories(String userId, String query, int limit) {
        UserMemory memory = userMemories.get(userId);
        if (memory == null) {
            return Collections.emptyList();
        }
        
        return memory.searchMemories(query, limit);
    }
    
    public void clearContext(String userId, String sessionId) {
        UserContext context = userContexts.get(userId);
        if (context != null) {
            context.clearSession(sessionId);
            logger.info("Cleared context for user: {}, session: {}", userId, sessionId);
        }
    }
    
    public void clearUserData(String userId) {
        userContexts.remove(userId);
        userMemories.remove(userId);
        logger.info("Cleared all data for user: {}", userId);
    }
    
    public int getUserContextCount(String userId) {
        UserContext context = userContexts.get(userId);
        return context != null ? context.getTotalInteractions() : 0;
    }
    
    public int getUserMemoryCount(String userId) {
        UserMemory memory = userMemories.get(userId);
        return memory != null ? memory.getMemoryCount() : 0;
    }
    
    private void performCleanup() {
        try {
            logger.debug("Starting context cleanup task...");
            
            // Clean up old contexts
            userContexts.values().forEach(UserContext::cleanup);
            
            // Clean up expired memories
            long cutoffTime = System.currentTimeMillis() - memoryTtl;
            userMemories.values().forEach(memory -> memory.cleanup(cutoffTime));
            
            logger.debug("Context cleanup task completed");
        } catch (Exception e) {
            logger.error("Error during context cleanup", e);
        }
    }
    
    public void shutdown() {
        cleanupExecutor.shutdown();
        try {
            if (!cleanupExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                cleanupExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            cleanupExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
        logger.info("ContextManager shutdown complete");
    }
    
    // Inner classes for context and memory management
    private static class UserContext {
        private final String userId;
        private final ConcurrentMap<String, List<ContextInteraction>> sessionInteractions = new ConcurrentHashMap<>();
        
        public UserContext(String userId) {
            this.userId = userId;
        }
        
        public void addInteraction(String sessionId, ModelRequest request, ModelResponse response) {
            sessionInteractions.computeIfAbsent(sessionId, k -> new ArrayList<>())
                .add(new ContextInteraction(request, response, LocalDateTime.now()));
        }
        
        public List<ContextInteraction> getInteractions(String sessionId) {
            if (sessionId == null) {
                // Return all interactions if no sessionId specified
                return sessionInteractions.values().stream()
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
            }
            return sessionInteractions.getOrDefault(sessionId, Collections.emptyList());
        }
        
        public void clearSession(String sessionId) {
            sessionInteractions.remove(sessionId);
        }
        
        public int getTotalInteractions() {
            return sessionInteractions.values().stream()
                .mapToInt(List::size)
                .sum();
        }
        
        public void cleanup() {
            // Remove sessions with too many interactions
            sessionInteractions.entrySet().removeIf(entry -> 
                entry.getValue().size() > 1000);
        }
    }
    
    private static class UserMemory {
        private final String userId;
        private final List<MemoryEntry> memories = new ArrayList<>();
        
        public UserMemory(String userId) {
            this.userId = userId;
        }
        
        public void addMemory(ModelRequest request, ModelResponse response) {
            memories.add(new MemoryEntry(request, response, System.currentTimeMillis()));
            
            // Keep only the most recent memories
            if (memories.size() > 1000) {
                memories.remove(0);
            }
        }
        
        public List<MemoryEntry> searchMemories(String query, int limit) {
            // Simple search implementation - can be enhanced with vector search
            return memories.stream()
                .filter(memory -> memory.matchesQuery(query))
                .limit(limit)
                .toList();
        }
        
        public int getMemoryCount() {
            return memories.size();
        }
        
        public void cleanup(long cutoffTime) {
            memories.removeIf(memory -> memory.timestamp < cutoffTime);
        }
    }
    
    private static class ContextInteraction {
        private final ModelRequest request;
        private final ModelResponse response;
        private final LocalDateTime timestamp;
        
        public ContextInteraction(ModelRequest request, ModelResponse response, LocalDateTime timestamp) {
            this.request = request;
            this.response = response;
            this.timestamp = timestamp;
        }
        
        // Getters
        public ModelRequest getRequest() { return request; }
        public ModelResponse getResponse() { return response; }
        public LocalDateTime getTimestamp() { return timestamp; }
    }
    
    private static class MemoryEntry {
        private final ModelRequest request;
        private final ModelResponse response;
        private final long timestamp;
        
        public MemoryEntry(ModelRequest request, ModelResponse response, long timestamp) {
            this.request = request;
            this.response = response;
            this.timestamp = timestamp;
        }
        
        public boolean matchesQuery(String query) {
            // Simple text matching - can be enhanced with semantic search
            String requestText = request.getSystemMessage() + " " + 
                               String.join(" ", request.getUserMessages());
            String responseText = response.getResponse();
            
            return requestText.toLowerCase().contains(query.toLowerCase()) ||
                   responseText.toLowerCase().contains(query.toLowerCase());
        }
        
        // Getters
        public ModelRequest getRequest() { return request; }
        public ModelResponse getResponse() { return response; }
        public long getTimestamp() { return timestamp; }
    }
}
