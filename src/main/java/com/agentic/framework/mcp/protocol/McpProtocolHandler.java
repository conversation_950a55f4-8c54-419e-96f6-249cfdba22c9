package com.agentic.framework.mcp.protocol;

import com.agentic.framework.mcp.ContextManager;
import com.agentic.framework.mcp.ModelRegistry;
import com.agentic.framework.mcp.tools.McpTool;
import com.agentic.framework.mcp.tools.ToolRegistry;
import com.agentic.framework.model.ModelRequest;
import com.agentic.framework.model.ModelResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * Handles MCP protocol messages and routes them to appropriate handlers
 */
@Component
public class McpProtocolHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(McpProtocolHandler.class);
    
    private final ModelRegistry modelRegistry;
    private final ContextManager contextManager;
    private final ToolRegistry toolRegistry;
    private final ObjectMapper objectMapper;
    
    // Session state
    private final Map<String, SessionState> sessions = new HashMap<>();
    
    @Autowired
    public McpProtocolHandler(ModelRegistry modelRegistry, ContextManager contextManager,
                              ToolRegistry toolRegistry, ObjectMapper objectMapper) {
        this.modelRegistry = modelRegistry;
        this.contextManager = contextManager;
        this.toolRegistry = toolRegistry;
        this.objectMapper = objectMapper;
    }
    
    /**
     * Process an MCP request
     */
    public CompletableFuture<McpProtocol.McpResponse> handleRequest(McpProtocol.McpRequest request, String clientId) {
        return CompletableFuture.supplyAsync(() -> {
            McpProtocol.McpResponse response = new McpProtocol.McpResponse();
            response.setId(request.getId());
            response.setJsonrpc(request.getJsonrpc());
            
            try {
                String method = request.getMethod();
                Map<String, Object> params = request.getParams();
                
                logger.debug("Processing MCP request: {} from client: {}", method, clientId);
                
                Object result = switch (method) {
                    case "initialize" -> handleInitialize(params, clientId);
                    case "initialized" -> handleInitialized(clientId);
                    case "tools/list" -> handleToolsList();
                    case "tools/call" -> handleToolCall(params, clientId);
                    case "resources/list" -> handleResourcesList(clientId);
                    case "resources/read" -> handleResourceRead(params, clientId);
                    case "prompts/list" -> handlePromptsList();
                    case "prompts/get" -> handlePromptGet(params);
                    case "completion" -> handleCompletion(params, clientId);
                    case "sampling/createMessage" -> handleSamplingCreateMessage(params, clientId);
                    case "shutdown" -> handleShutdown(clientId);
                    default -> throw new IllegalArgumentException("Unknown method: " + method);
                };
                
                response.setResult(result);
                
            } catch (Exception e) {
                logger.error("Error handling MCP request", e);
                McpProtocol.McpError error = new McpProtocol.McpError(
                    -32603, // Internal error
                    "Internal error: " + e.getMessage()
                );
                response.setError(error);
            }
            
            return response;
        });
    }
    
    private Map<String, Object> handleInitialize(Map<String, Object> params, String clientId) {
        // Store client capabilities
        SessionState session = new SessionState();
        session.clientId = clientId;
        session.initialized = true;
        
        if (params != null && params.containsKey("capabilities")) {
            session.clientCapabilities = objectMapper.convertValue(
                params.get("capabilities"),
                McpProtocol.ClientCapabilities.class
            );
        }
        
        sessions.put(clientId, session);
        
        // Return server capabilities
        Map<String, Object> serverInfo = new HashMap<>();
        serverInfo.put("name", "Agentic Framework MCP Server");
        serverInfo.put("version", "1.0.0");
        
        McpProtocol.ServerCapabilities capabilities = new McpProtocol.ServerCapabilities();
        capabilities.setTools(Map.of("listChanged", true));
        capabilities.setResources(Map.of("subscribe", true, "listChanged", true));
        capabilities.setPrompts(Map.of("listChanged", true));
        
        Map<String, Object> result = new HashMap<>();
        result.put("protocolVersion", "2024-11-05");
        result.put("capabilities", capabilities);
        result.put("serverInfo", serverInfo);
        
        logger.info("Client {} initialized successfully", clientId);
        return result;
    }
    
    private Map<String, Object> handleInitialized(String clientId) {
        SessionState session = sessions.get(clientId);
        if (session != null) {
            session.ready = true;
        }
        return Map.of("success", true);
    }
    
    private Map<String, Object> handleToolsList() {
        List<McpProtocol.Tool> tools = toolRegistry.getToolDefinitions();
        return Map.of("tools", tools);
    }
    
    private Object handleToolCall(Map<String, Object> params, String clientId) {
        String toolName = (String) params.get("name");
        Map<String, Object> arguments = (Map<String, Object>) params.get("arguments");
        
        McpTool tool = toolRegistry.getTool(toolName);
        if (tool == null) {
            throw new IllegalArgumentException("Tool not found: " + toolName);
        }
        
        // Get session info
        SessionState session = sessions.get(clientId);
        String userId = session != null ? session.userId : clientId;
        String sessionId = session != null ? session.sessionId : UUID.randomUUID().toString();
        
        // Execute tool asynchronously
        try {
            CompletableFuture<Object> future = tool.execute(arguments, userId, sessionId);
            return future.get(); // Wait for completion
        } catch (Exception e) {
            logger.error("Tool execution failed: {}", toolName, e);
            throw new RuntimeException("Tool execution failed: " + e.getMessage());
        }
    }
    
    private Map<String, Object> handleResourcesList(String clientId) {
        // List available resources (contexts and memories)
        SessionState session = sessions.get(clientId);
        String userId = session != null ? session.userId : clientId;
        
        List<McpProtocol.Resource> resources = new ArrayList<>();
        
        // Add context resource
        McpProtocol.Resource contextResource = new McpProtocol.Resource();
        contextResource.setUri("context://" + userId);
        contextResource.setName("User Context");
        contextResource.setDescription("Conversation context and history");
        contextResource.setMimeType("application/json");
        resources.add(contextResource);
        
        // Add memory resource
        McpProtocol.Resource memoryResource = new McpProtocol.Resource();
        memoryResource.setUri("memory://" + userId);
        memoryResource.setName("User Memory");
        memoryResource.setDescription("Long-term memory storage");
        memoryResource.setMimeType("application/json");
        resources.add(memoryResource);
        
        return Map.of("resources", resources);
    }
    
    private Object handleResourceRead(Map<String, Object> params, String clientId) {
        String uri = (String) params.get("uri");
        
        SessionState session = sessions.get(clientId);
        String userId = session != null ? session.userId : clientId;
        String sessionId = session != null ? session.sessionId : UUID.randomUUID().toString();
        
        try {
            if (uri.startsWith("context://")) {
                // Read context
                Object context = contextManager.getContext(userId, sessionId, 100);
                return Map.of(
                    "contents", List.of(Map.of(
                        "uri", uri,
                        "mimeType", "application/json",
                        "text", objectMapper.writeValueAsString(context)
                    ))
                );
            } else if (uri.startsWith("memory://")) {
                // Read memories
                Object memories = contextManager.getMemories(userId, "", 100);
                return Map.of(
                    "contents", List.of(Map.of(
                        "uri", uri,
                        "mimeType", "application/json",
                        "text", objectMapper.writeValueAsString(memories)
                    ))
                );
            } else {
                throw new IllegalArgumentException("Unknown resource URI: " + uri);
            }
        } catch (Exception e) {
            throw new RuntimeException("Error reading resource: " + uri, e);
        }
    }
    
    private Map<String, Object> handlePromptsList() {
        // List available prompts
        List<McpProtocol.Prompt> prompts = new ArrayList<>();
        
        // Add some example prompts
        McpProtocol.Prompt summarizePrompt = new McpProtocol.Prompt();
        summarizePrompt.setName("summarize");
        summarizePrompt.setDescription("Summarize a conversation or text");
        summarizePrompt.setArguments(List.of(
            createPromptArgument("text", "Text to summarize", true),
            createPromptArgument("style", "Summary style (brief, detailed, bullet)", false)
        ));
        prompts.add(summarizePrompt);
        
        McpProtocol.Prompt analyzePrompt = new McpProtocol.Prompt();
        analyzePrompt.setName("analyze");
        analyzePrompt.setDescription("Analyze data or patterns");
        analyzePrompt.setArguments(List.of(
            createPromptArgument("data", "Data to analyze", true),
            createPromptArgument("type", "Analysis type", false)
        ));
        prompts.add(analyzePrompt);
        
        return Map.of("prompts", prompts);
    }
    
    private McpProtocol.PromptArgument createPromptArgument(String name, String description, boolean required) {
        McpProtocol.PromptArgument arg = new McpProtocol.PromptArgument();
        arg.setName(name);
        arg.setDescription(description);
        arg.setRequired(required);
        return arg;
    }
    
    private Object handlePromptGet(Map<String, Object> params) {
        String name = (String) params.get("name");
        Map<String, Object> arguments = (Map<String, Object>) params.get("arguments");
        
        String promptText = switch (name) {
            case "summarize" -> {
                String text = (String) arguments.get("text");
                String style = (String) arguments.getOrDefault("style", "brief");
                yield String.format("Please provide a %s summary of the following:\n\n%s", style, text);
            }
            case "analyze" -> {
                String data = (String) arguments.get("data");
                String type = (String) arguments.getOrDefault("type", "general");
                yield String.format("Please perform a %s analysis of the following data:\n\n%s", type, data);
            }
            default -> throw new IllegalArgumentException("Unknown prompt: " + name);
        };
        
        return Map.of(
            "messages", List.of(Map.of(
                "role", "user",
                "content", Map.of(
                    "type", "text",
                    "text", promptText
                )
            ))
        );
    }
    
    private Object handleCompletion(Map<String, Object> params, String clientId) {
        // Handle completion request
        List<Map<String, Object>> messages = (List<Map<String, Object>>) params.get("messages");
        String modelName = (String) params.getOrDefault("model", "gpt-4");
        
        SessionState session = sessions.get(clientId);
        String userId = session != null ? session.userId : clientId;
        String sessionId = session != null ? session.sessionId : UUID.randomUUID().toString();
        
        // Build model request
        ModelRequest request = new ModelRequest();
        request.setModelName(modelName);
        request.setUserId(userId);
        request.setSessionId(sessionId);
        
        // Extract messages
        List<String> userMessages = new ArrayList<>();
        String systemMessage = "You are a helpful AI assistant.";
        
        for (Map<String, Object> msg : messages) {
            String role = (String) msg.get("role");
            Map<String, Object> content = (Map<String, Object>) msg.get("content");
            String text = (String) content.get("text");
            
            if ("system".equals(role)) {
                systemMessage = text;
            } else {
                userMessages.add(text);
            }
        }
        
        request.setSystemMessage(systemMessage);
        request.setUserMessages(userMessages);
        
        // Process through model
        try {
            var modelProvider = modelRegistry.getModelProvider(modelName);
            CompletableFuture<ModelResponse> future = modelProvider.processRequest(request, modelProvider.getDefaultConfig());
            ModelResponse response = future.get();
            
            // Store in context
            contextManager.addContext(userId, sessionId, request, response);
            
            return Map.of(
                "completion", Map.of(
                    "choices", List.of(Map.of(
                        "message", Map.of(
                            "role", "assistant",
                            "content", response.getResponse()
                        )
                    ))
                )
            );
        } catch (Exception e) {
            logger.error("Completion failed", e);
            throw new RuntimeException("Completion failed: " + e.getMessage());
        }
    }
    
    private Object handleSamplingCreateMessage(Map<String, Object> params, String clientId) {
        // Similar to completion but for sampling
        return handleCompletion(params, clientId);
    }
    
    private Map<String, Object> handleShutdown(String clientId) {
        sessions.remove(clientId);
        logger.info("Client {} disconnected", clientId);
        return Map.of("success", true);
    }
    
    // Session state class
    private static class SessionState {
        String clientId;
        String userId;
        String sessionId = UUID.randomUUID().toString();
        boolean initialized = false;
        boolean ready = false;
        McpProtocol.ClientCapabilities clientCapabilities;
    }
}