package com.agentic.framework.mcp.protocol;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import java.util.Map;
import java.util.List;

/**
 * MCP Protocol Message Definitions
 * Based on Model Context Protocol specification
 */
public class McpProtocol {
    
    // Base message structure
    public static class McpMessage {
        @JsonProperty("jsonrpc")
        private String jsonrpc = "2.0";
        
        @JsonProperty("id")
        private String id;
        
        public String getJsonrpc() { return jsonrpc; }
        public void setJsonrpc(String jsonrpc) { this.jsonrpc = jsonrpc; }
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
    }
    
    // Request message
    public static class McpRequest extends McpMessage {
        @JsonProperty("method")
        private String method;
        
        @JsonProperty("params")
        private Map<String, Object> params;
        
        public String getMethod() { return method; }
        public void setMethod(String method) { this.method = method; }
        public Map<String, Object> getParams() { return params; }
        public void setParams(Map<String, Object> params) { this.params = params; }
    }
    
    // Response message
    public static class McpResponse extends McpMessage {
        @JsonProperty("result")
        private Object result;
        
        @JsonProperty("error")
        private McpError error;
        
        public Object getResult() { return result; }
        public void setResult(Object result) { this.result = result; }
        public McpError getError() { return error; }
        public void setError(McpError error) { this.error = error; }
    }
    
    // Error structure
    public static class McpError {
        @JsonProperty("code")
        private int code;
        
        @JsonProperty("message")
        private String message;
        
        @JsonProperty("data")
        private Object data;
        
        public McpError() {}
        
        public McpError(int code, String message) {
            this.code = code;
            this.message = message;
        }
        
        public int getCode() { return code; }
        public void setCode(int code) { this.code = code; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public Object getData() { return data; }
        public void setData(Object data) { this.data = data; }
    }
    
    // Tool definition
    public static class Tool {
        @JsonProperty("name")
        private String name;
        
        @JsonProperty("description")
        private String description;
        
        @JsonProperty("inputSchema")
        private JsonNode inputSchema;
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public JsonNode getInputSchema() { return inputSchema; }
        public void setInputSchema(JsonNode inputSchema) { this.inputSchema = inputSchema; }
    }
    
    // Resource definition
    public static class Resource {
        @JsonProperty("uri")
        private String uri;
        
        @JsonProperty("name")
        private String name;
        
        @JsonProperty("description")
        private String description;
        
        @JsonProperty("mimeType")
        private String mimeType;
        
        public String getUri() { return uri; }
        public void setUri(String uri) { this.uri = uri; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public String getMimeType() { return mimeType; }
        public void setMimeType(String mimeType) { this.mimeType = mimeType; }
    }
    
    // Prompt definition
    public static class Prompt {
        @JsonProperty("name")
        private String name;
        
        @JsonProperty("description")
        private String description;
        
        @JsonProperty("arguments")
        private List<PromptArgument> arguments;
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public List<PromptArgument> getArguments() { return arguments; }
        public void setArguments(List<PromptArgument> arguments) { this.arguments = arguments; }
    }
    
    public static class PromptArgument {
        @JsonProperty("name")
        private String name;
        
        @JsonProperty("description")
        private String description;
        
        @JsonProperty("required")
        private boolean required;
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public boolean isRequired() { return required; }
        public void setRequired(boolean required) { this.required = required; }
    }
    
    // Server capabilities
    public static class ServerCapabilities {
        @JsonProperty("tools")
        private Map<String, Object> tools;
        
        @JsonProperty("resources")
        private Map<String, Object> resources;
        
        @JsonProperty("prompts")
        private Map<String, Object> prompts;
        
        public Map<String, Object> getTools() { return tools; }
        public void setTools(Map<String, Object> tools) { this.tools = tools; }
        public Map<String, Object> getResources() { return resources; }
        public void setResources(Map<String, Object> resources) { this.resources = resources; }
        public Map<String, Object> getPrompts() { return prompts; }
        public void setPrompts(Map<String, Object> prompts) { this.prompts = prompts; }
    }
    
    // Client capabilities
    public static class ClientCapabilities {
        @JsonProperty("experimental")
        private Map<String, Object> experimental;
        
        @JsonProperty("sampling")
        private Map<String, Object> sampling;
        
        public Map<String, Object> getExperimental() { return experimental; }
        public void setExperimental(Map<String, Object> experimental) { this.experimental = experimental; }
        public Map<String, Object> getSampling() { return sampling; }
        public void setSampling(Map<String, Object> sampling) { this.sampling = sampling; }
    }
}