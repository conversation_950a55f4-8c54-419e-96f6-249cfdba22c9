package com.agentic.framework.mcp.storage;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Persistent storage for MCP context and memories
 * Provides file-based persistence with automatic backup
 */
@Component
public class PersistentStorage {
    
    private static final Logger logger = LoggerFactory.getLogger(PersistentStorage.class);
    
    private final Path storageRoot;
    private final ObjectMapper objectMapper;
    private final ScheduledExecutorService persistExecutor;
    private final Map<String, Object> dirtyData = new ConcurrentHashMap<>();
    
    @Value("${mcp.storage.persist-interval:30000}")
    private long persistInterval;
    
    @Value("${mcp.storage.backup-enabled:true}")
    private boolean backupEnabled;
    
    @Value("${mcp.storage.max-backups:5}")
    private int maxBackups;
    
    public PersistentStorage(@Value("${mcp.storage.path:./mcp-storage}") String storagePath) {
        this.storageRoot = Paths.get(storagePath).toAbsolutePath();
        this.objectMapper = new ObjectMapper();
        this.persistExecutor = Executors.newSingleThreadScheduledExecutor();
        
        try {
            Files.createDirectories(storageRoot);
            Files.createDirectories(storageRoot.resolve("contexts"));
            Files.createDirectories(storageRoot.resolve("memories"));
            Files.createDirectories(storageRoot.resolve("backups"));
        } catch (IOException e) {
            throw new RuntimeException("Failed to create storage directories", e);
        }
    }
    
    @PostConstruct
    public void startPersistence() {
        // Schedule periodic persistence
        persistExecutor.scheduleAtFixedRate(
            this::persistDirtyData,
            persistInterval,
            persistInterval,
            TimeUnit.MILLISECONDS
        );
        logger.info("Persistent storage initialized at: {}", storageRoot);
    }
    
    @PreDestroy
    public void shutdown() {
        // Final persist before shutdown
        persistDirtyData();
        
        persistExecutor.shutdown();
        try {
            if (!persistExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                persistExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            persistExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
        logger.info("Persistent storage shutdown complete");
    }
    
    /**
     * Save context data for a user
     */
    public void saveContext(String userId, String sessionId, Object contextData) {
        String key = "context:" + userId + ":" + sessionId;
        dirtyData.put(key, contextData);
    }
    
    /**
     * Load context data for a user
     */
    public <T> T loadContext(String userId, String sessionId, Class<T> type) {
        try {
            Path contextFile = storageRoot.resolve("contexts")
                .resolve(userId)
                .resolve(sessionId + ".json");
            
            if (Files.exists(contextFile)) {
                String json = Files.readString(contextFile);
                return objectMapper.readValue(json, type);
            }
        } catch (IOException e) {
            logger.error("Failed to load context for user: {}, session: {}", userId, sessionId, e);
        }
        return null;
    }
    
    /**
     * Save memory data for a user
     */
    public void saveMemory(String userId, Object memoryData) {
        String key = "memory:" + userId;
        dirtyData.put(key, memoryData);
    }
    
    /**
     * Load memory data for a user
     */
    public <T> T loadMemory(String userId, Class<T> type) {
        try {
            Path memoryFile = storageRoot.resolve("memories")
                .resolve(userId + ".json");
            
            if (Files.exists(memoryFile)) {
                String json = Files.readString(memoryFile);
                return objectMapper.readValue(json, type);
            }
        } catch (IOException e) {
            logger.error("Failed to load memory for user: {}", userId, e);
        }
        return null;
    }
    
    /**
     * List all stored contexts for a user
     */
    public List<String> listUserContexts(String userId) {
        List<String> contexts = new ArrayList<>();
        Path userContextDir = storageRoot.resolve("contexts").resolve(userId);
        
        if (Files.exists(userContextDir)) {
            try (DirectoryStream<Path> stream = Files.newDirectoryStream(userContextDir, "*.json")) {
                for (Path path : stream) {
                    String filename = path.getFileName().toString();
                    contexts.add(filename.substring(0, filename.length() - 5)); // Remove .json
                }
            } catch (IOException e) {
                logger.error("Failed to list contexts for user: {}", userId, e);
            }
        }
        
        return contexts;
    }
    
    /**
     * Delete context data for a user session
     */
    public void deleteContext(String userId, String sessionId) {
        try {
            Path contextFile = storageRoot.resolve("contexts")
                .resolve(userId)
                .resolve(sessionId + ".json");
            
            if (Files.exists(contextFile)) {
                if (backupEnabled) {
                    backupFile(contextFile);
                }
                Files.delete(contextFile);
                logger.info("Deleted context for user: {}, session: {}", userId, sessionId);
            }
        } catch (IOException e) {
            logger.error("Failed to delete context", e);
        }
    }
    
    /**
     * Delete all data for a user
     */
    public void deleteUserData(String userId) {
        try {
            // Delete contexts
            Path userContextDir = storageRoot.resolve("contexts").resolve(userId);
            if (Files.exists(userContextDir)) {
                Files.walk(userContextDir)
                    .sorted(Comparator.reverseOrder())
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                        } catch (IOException e) {
                            logger.error("Failed to delete: {}", path, e);
                        }
                    });
            }
            
            // Delete memories
            Path memoryFile = storageRoot.resolve("memories").resolve(userId + ".json");
            if (Files.exists(memoryFile)) {
                if (backupEnabled) {
                    backupFile(memoryFile);
                }
                Files.delete(memoryFile);
            }
            
            logger.info("Deleted all data for user: {}", userId);
        } catch (IOException e) {
            logger.error("Failed to delete user data", e);
        }
    }
    
    /**
     * Persist dirty data to disk
     */
    private void persistDirtyData() {
        if (dirtyData.isEmpty()) {
            return;
        }
        
        Map<String, Object> dataToPersist = new HashMap<>(dirtyData);
        dirtyData.clear();
        
        for (Map.Entry<String, Object> entry : dataToPersist.entrySet()) {
            String key = entry.getKey();
            Object data = entry.getValue();
            
            try {
                if (key.startsWith("context:")) {
                    String[] parts = key.split(":");
                    String userId = parts[1];
                    String sessionId = parts[2];
                    persistContext(userId, sessionId, data);
                } else if (key.startsWith("memory:")) {
                    String userId = key.substring(7);
                    persistMemory(userId, data);
                }
            } catch (Exception e) {
                logger.error("Failed to persist data for key: {}", key, e);
            }
        }
    }
    
    private void persistContext(String userId, String sessionId, Object data) throws IOException {
        Path userDir = storageRoot.resolve("contexts").resolve(userId);
        Files.createDirectories(userDir);
        
        Path contextFile = userDir.resolve(sessionId + ".json");
        String json = objectMapper.writeValueAsString(data);
        Files.writeString(contextFile, json, StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
        
        logger.debug("Persisted context for user: {}, session: {}", userId, sessionId);
    }
    
    private void persistMemory(String userId, Object data) throws IOException {
        Path memoryFile = storageRoot.resolve("memories").resolve(userId + ".json");
        Files.createDirectories(memoryFile.getParent());
        
        String json = objectMapper.writeValueAsString(data);
        Files.writeString(memoryFile, json, StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
        
        logger.debug("Persisted memory for user: {}", userId);
    }
    
    private void backupFile(Path file) {
        try {
            String filename = file.getFileName().toString();
            String timestamp = String.valueOf(System.currentTimeMillis());
            Path backupFile = storageRoot.resolve("backups")
                .resolve(timestamp + "_" + filename);
            
            Files.copy(file, backupFile, StandardCopyOption.REPLACE_EXISTING);
            
            // Clean old backups
            cleanOldBackups(filename);
        } catch (IOException e) {
            logger.error("Failed to backup file: {}", file, e);
        }
    }
    
    private void cleanOldBackups(String filename) {
        try {
            Path backupDir = storageRoot.resolve("backups");
            List<Path> backups = new ArrayList<>();
            
            try (DirectoryStream<Path> stream = Files.newDirectoryStream(backupDir, "*_" + filename)) {
                for (Path path : stream) {
                    backups.add(path);
                }
            }
            
            if (backups.size() > maxBackups) {
                backups.sort(Comparator.comparing(p -> p.getFileName().toString()));
                for (int i = 0; i < backups.size() - maxBackups; i++) {
                    Files.delete(backups.get(i));
                }
            }
        } catch (IOException e) {
            logger.error("Failed to clean old backups", e);
        }
    }
    
    /**
     * Get storage statistics
     */
    public Map<String, Object> getStorageStats() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            long contextCount = Files.walk(storageRoot.resolve("contexts"))
                .filter(Files::isRegularFile)
                .count();
            
            long memoryCount = Files.walk(storageRoot.resolve("memories"))
                .filter(Files::isRegularFile)
                .count();
            
            long totalSize = Files.walk(storageRoot)
                .filter(Files::isRegularFile)
                .mapToLong(path -> {
                    try {
                        return Files.size(path);
                    } catch (IOException e) {
                        return 0;
                    }
                }).sum();
            
            stats.put("contextCount", contextCount);
            stats.put("memoryCount", memoryCount);
            stats.put("totalSizeBytes", totalSize);
            stats.put("totalSizeMB", totalSize / (1024.0 * 1024.0));
        } catch (IOException e) {
            logger.error("Failed to get storage stats", e);
        }
        
        return stats;
    }
}