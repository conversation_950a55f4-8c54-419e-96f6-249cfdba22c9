package com.agentic.framework.mcp;

import com.agentic.framework.mcp.protocol.McpProtocol;
import com.agentic.framework.mcp.protocol.McpProtocolHandler;
import com.agentic.framework.model.ModelRequest;
import com.agentic.framework.model.ModelResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.Socket;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Handles individual client connections in the MCP server
 * Manages communication protocol and request processing
 */
public class ClientHand<PERSON> implements Runnable {
    
    private static final Logger logger = LoggerFactory.getLogger(ClientHandler.class);
    
    private final Socket clientSocket;
    private final ModelRegistry modelRegistry;
    private final ContextManager contextManager;
    private final McpProtocolHandler protocolHandler;
    private final ObjectMapper objectMapper;
    private final String clientId;
    
    public ClientHandler(Socket clientSocket, ModelRegistry modelRegistry, 
                        ContextManager contextManager, McpProtocolHandler protocolHandler) {
        this.clientSocket = clientSocket;
        this.modelRegistry = modelRegistry;
        this.contextManager = contextManager;
        this.protocolHandler = protocolHandler;
        this.objectMapper = new ObjectMapper();
        this.clientId = UUID.randomUUID().toString();
    }
    
    @Override
    public void run() {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(clientSocket.getInputStream()));
             PrintWriter writer = new PrintWriter(clientSocket.getOutputStream(), true)) {
            
            logger.info("Client handler started for client: {}", clientId);
            
            String inputLine;
            while ((inputLine = reader.readLine()) != null) {
                try {
                    // Check if it's an MCP protocol message (JSONRPC)
                    if (inputLine.contains("jsonrpc")) {
                        handleMcpProtocol(inputLine, writer);
                    } else {
                        // Handle legacy format
                        handleLegacyProtocol(inputLine, writer);
                    }
                    
                } catch (Exception e) {
                    logger.error("Error processing client request", e);
                    sendErrorResponse(writer, e.getMessage());
                }
            }
            
        } catch (IOException e) {
            logger.error("Error in client handler for client: {}", clientId, e);
        } finally {
            try {
                // Send shutdown notification if using MCP protocol
                protocolHandler.handleRequest(createShutdownRequest(), clientId);
                clientSocket.close();
                logger.info("Client connection closed for client: {}", clientId);
            } catch (Exception e) {
                logger.error("Error closing client socket", e);
            }
        }
    }
    
    private void handleMcpProtocol(String inputLine, PrintWriter writer) throws Exception {
        // Parse as MCP protocol request
        McpProtocol.McpRequest request = objectMapper.readValue(inputLine, McpProtocol.McpRequest.class);
        
        logger.debug("Processing MCP request from client {}: {}", clientId, request.getMethod());
        
        // Process through protocol handler
        CompletableFuture<McpProtocol.McpResponse> responseFuture = 
            protocolHandler.handleRequest(request, clientId);
        
        // Wait for response (with timeout)
        McpProtocol.McpResponse response = responseFuture.get(60, TimeUnit.SECONDS);
        
        // Send response back to client
        String responseJson = objectMapper.writeValueAsString(response);
        writer.println(responseJson);
    }
    
    private void handleLegacyProtocol(String inputLine, PrintWriter writer) throws Exception {
        // Parse the incoming request
        McpRequest request = objectMapper.readValue(inputLine, McpRequest.class);
        
        // Process the request
        McpResponse response = processLegacyRequest(request);
        
        // Send response back to client
        String responseJson = objectMapper.writeValueAsString(response);
        writer.println(responseJson);
    }
    
    private McpResponse processLegacyRequest(McpRequest request) {
        McpResponse response = new McpResponse();
        
        try {
            switch (request.getType()) {
                case "model_request":
                    response = handleModelRequest(request);
                    break;
                case "get_models":
                    response = handleGetModels(request);
                    break;
                case "get_context":
                    response = handleGetContext(request);
                    break;
                case "clear_context":
                    response = handleClearContext(request);
                    break;
                default:
                    response.setSuccess(false);
                    response.setError("Unknown request type: " + request.getType());
            }
        } catch (Exception e) {
            logger.error("Error processing request type: {}", request.getType(), e);
            response.setSuccess(false);
            response.setError("Internal error: " + e.getMessage());
        }
        
        return response;
    }
    
    private McpResponse handleModelRequest(McpRequest request) {
        McpResponse response = new McpResponse();
        
        try {
            // Extract model request data
            ModelRequest modelRequest = objectMapper.convertValue(request.getData(), ModelRequest.class);
            
            // Set client ID as user ID if not provided
            if (modelRequest.getUserId() == null) {
                modelRequest.setUserId(clientId);
            }
            
            // Generate session ID if not provided
            if (modelRequest.getSessionId() == null) {
                modelRequest.setSessionId(UUID.randomUUID().toString());
            }
            
            // Get model provider and process request
            var modelProvider = modelRegistry.getModelProvider(modelRequest.getModelName());
            CompletableFuture<ModelResponse> futureResponse = modelProvider.processRequest(
                modelRequest, 
                modelProvider.getDefaultConfig()
            );
            
            // Wait for response
            ModelResponse modelResponse = futureResponse.get(60, TimeUnit.SECONDS);
            
            // Add to context
            contextManager.addContext(
                modelRequest.getUserId(), 
                modelRequest.getSessionId(), 
                modelRequest, 
                modelResponse
            );
            
            // Build response
            response.setSuccess(true);
            response.setData(modelResponse);
            
        } catch (Exception e) {
            logger.error("Error handling model request", e);
            response.setSuccess(false);
            response.setError("Model request failed: " + e.getMessage());
        }
        
        return response;
    }
    
    private McpResponse handleGetModels(McpRequest request) {
        McpResponse response = new McpResponse();
        
        try {
            var models = modelRegistry.getAllModels();
            response.setSuccess(true);
            response.setData(models);
            
        } catch (Exception e) {
            logger.error("Error getting models", e);
            response.setSuccess(false);
            response.setError("Failed to get models: " + e.getMessage());
        }
        
        return response;
    }
    
    private McpResponse handleGetContext(McpRequest request) {
        McpResponse response = new McpResponse();

        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> data = (Map<String, Object>) request.getData();
            String userId = (String) data.getOrDefault("userId", clientId);
            String sessionId = (String) data.get("sessionId");
            Integer limit = (Integer) data.getOrDefault("limit", 10);

            var context = contextManager.getContext(userId, sessionId, limit);
            response.setSuccess(true);
            response.setData(context);

        } catch (Exception e) {
            logger.error("Error getting context", e);
            response.setSuccess(false);
            response.setError("Failed to get context: " + e.getMessage());
        }

        return response;
    }
    
    private McpResponse handleClearContext(McpRequest request) {
        McpResponse response = new McpResponse();

        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> data = (Map<String, Object>) request.getData();
            String userId = (String) data.getOrDefault("userId", clientId);
            String sessionId = (String) data.get("sessionId");

            if (sessionId != null) {
                contextManager.clearContext(userId, sessionId);
            } else {
                contextManager.clearUserData(userId);
            }

            response.setSuccess(true);
            response.setData("Context cleared successfully");

        } catch (Exception e) {
            logger.error("Error clearing context", e);
            response.setSuccess(false);
            response.setError("Failed to clear context: " + e.getMessage());
        }

        return response;
    }
    
    private void sendErrorResponse(PrintWriter writer, String errorMessage) {
        try {
            McpProtocol.McpResponse errorResponse = new McpProtocol.McpResponse();
            errorResponse.setJsonrpc("2.0");
            errorResponse.setError(new McpProtocol.McpError(-32603, errorMessage));
            
            String errorJson = objectMapper.writeValueAsString(errorResponse);
            writer.println(errorJson);
        } catch (Exception e) {
            logger.error("Failed to send error response", e);
        }
    }
    
    private McpProtocol.McpRequest createShutdownRequest() {
        McpProtocol.McpRequest request = new McpProtocol.McpRequest();
        request.setJsonrpc("2.0");
        request.setMethod("shutdown");
        request.setId(UUID.randomUUID().toString());
        return request;
    }
    
    // Inner classes for legacy MCP protocol
    private static class McpRequest {
        private String type;
        private Object data;
        
        // Getters and setters
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        public Object getData() { return data; }
        public void setData(Object data) { this.data = data; }
    }
    
    private static class McpResponse {
        private boolean success;
        private Object data;
        private String error;
        
        // Getters and setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public Object getData() { return data; }
        public void setData(Object data) { this.data = data; }
        public String getError() { return error; }
        public void setError(String error) { this.error = error; }
    }
}