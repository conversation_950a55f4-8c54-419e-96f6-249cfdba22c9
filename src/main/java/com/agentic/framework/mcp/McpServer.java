package com.agentic.framework.mcp;

import com.agentic.framework.config.McpServerConfig;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.io.IOException;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * MCP (Model Context Protocol) Server implementation
 * Handles client connections and manages model interactions
 */
@Component
public class McpServer {
    
    private static final Logger logger = LoggerFactory.getLogger(McpServer.class);
    
    private final McpServerConfig config;
    private final ModelRegistry modelRegistry;
    private final ContextManager contextManager;
    
    private ServerSocket serverSocket;
    private ExecutorService executorService;
    private AtomicBoolean running = new AtomicBoolean(false);
    
    @Autowired
    public McpServer(McpServerConfig config, ModelRegistry modelRegistry, ContextManager contextManager) {
        this.config = config;
        this.modelRegistry = modelRegistry;
        this.contextManager = contextManager;
    }
    
    @PostConstruct
    public void start() {
        try {
            initializeServer();
            startServer();
            logger.info("MCP Server started successfully on {}:{}", config.getHost(), config.getPort());
        } catch (IOException e) {
            logger.error("Failed to start MCP Server", e);
            throw new RuntimeException("Failed to start MCP Server", e);
        }
    }
    
    @PreDestroy
    public void stop() {
        shutdown();
    }
    
    private void initializeServer() throws IOException {
        serverSocket = new ServerSocket(config.getPort());
        executorService = Executors.newFixedThreadPool(config.getMaxConnections());
        running.set(true);
    }
    
    private void startServer() {
        new Thread(() -> {
            while (running.get() && !serverSocket.isClosed()) {
                try {
                    Socket clientSocket = serverSocket.accept();
                    clientSocket.setSoTimeout(config.getReadTimeout());
                    
                    if (running.get()) {
                        ClientHandler clientHandler = new ClientHandler(clientSocket, modelRegistry, contextManager);
                        executorService.submit(clientHandler);
                        logger.debug("New client connection accepted: {}", clientSocket.getInetAddress());
                    } else {
                        clientSocket.close();
                    }
                } catch (IOException e) {
                    if (running.get()) {
                        logger.error("Error accepting client connection", e);
                    }
                }
            }
        }, "MCP-Server-Acceptor").start();
    }
    
    public void shutdown() {
        logger.info("Shutting down MCP Server...");
        running.set(false);
        
        if (executorService != null) {
            executorService.shutdown();
        }
        
        if (serverSocket != null && !serverSocket.isClosed()) {
            try {
                serverSocket.close();
            } catch (IOException e) {
                logger.error("Error closing server socket", e);
            }
        }
        
        logger.info("MCP Server shutdown complete");
    }
    
    public boolean isRunning() {
        return running.get();
    }
    
    public int getActiveConnections() {
        return executorService != null ? ((java.util.concurrent.ThreadPoolExecutor) executorService).getActiveCount() : 0;
    }
}
