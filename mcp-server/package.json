{"name": "agentic-mcp-server", "version": "1.0.0", "description": "Real MCP Server implementation with AI model integration and context management", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsx src/index.ts", "test": "jest", "lint": "eslint src/**/*.ts", "clean": "rm -rf dist"}, "keywords": ["mcp", "model-context-protocol", "ai", "anthropic", "openai", "claude"], "author": "Agentic Framework", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0", "@anthropic-ai/sdk": "^0.27.0", "openai": "^4.67.0", "axios": "^1.7.0", "dotenv": "^16.4.0", "zod": "^3.22.0", "uuid": "^10.0.0", "sqlite3": "^5.1.0", "better-sqlite3": "^9.0.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "jest": "^29.0.0", "tsx": "^4.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0"}}