# Agentic Framework

A powerful Java-based framework for building AI agents with Model Context Protocol (MCP) support. This framework provides a unified interface to interact with multiple AI models including OpenAI GPT, Anthropic Claude, and local models.

## Features

- 🤖 **Multi-Model Support**: OpenAI GPT, Anthropic Claude, and local models
- 🔌 **MCP Protocol**: Full Model Context Protocol (JSONRPC 2.0) implementation
- 🧠 **Context Management**: Persistent conversation context and memory
- 🛠️ **Tool Integration**: Built-in tools (calculator, file system, memory)
- 📡 **Dual API**: REST API and MCP server endpoints
- 🔄 **Legacy Support**: Backward compatible with legacy protocol
- 🏗️ **Spring Boot**: Enterprise-ready with Spring Boot framework

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   REST API      │    │   MCP Server    │    │  Model Providers│
│   (Port 8080)   │    │   (Port 8081)   │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │ Model Registry  │
                    │ Context Manager │
                    │  Tool Registry  │
                    └─────────────────┘
```

## Quick Start

### Prerequisites

- Java 17 or higher
- Maven 3.6+
- API keys for OpenAI and/or Anthropic (optional)

### Installation

1. **Clone and build**:
```bash
git clone <repository-url>
cd agentic-framework
./start.sh
```

2. **Manual build**:
```bash
mvn clean package
java -jar target/agentic-framework-1.0.0.jar
```

### Configuration

Configure API keys in `src/main/resources/application.yml`:

```yaml
openai:
  api-key: your-openai-api-key
  base-url: https://api.openai.com/v1

anthropic:
  api-key: your-anthropic-api-key
  base-url: https://api.anthropic.com

mcp:
  server:
    port: 8081
    host: localhost
```

## REST API Usage

### Health Check

```bash
curl -X GET http://localhost:8080/api/v1/health
```

### List Available Models

```bash
curl -X GET http://localhost:8080/api/v1/models
```

### Process Model Request

```bash
curl -X POST http://localhost:8080/api/v1/models/process \
  -H "Content-Type: application/json" \
  -d '{
    "modelName": "claude-3-5-sonnet-20241022",
    "userId": "user123",
    "sessionId": "session456",
    "userMessages": ["What is quantum computing?"],
    "systemMessage": "You are a helpful AI assistant.",
    "maxTokens": 500,
    "temperature": 0.7
  }'
```

### Get User Context

```bash
curl -X GET "http://localhost:8080/api/v1/context/user123/session456?limit=10"
```

### Clear User Context

```bash
curl -X DELETE http://localhost:8080/api/v1/context/user123/session456
```

### Search Memories

```bash
curl -X GET "http://localhost:8080/api/v1/memories/user123/search?query=quantum&limit=5"
```

## MCP Protocol Usage

The MCP server runs on port 8081 and supports the Model Context Protocol specification.

### Initialize Connection

```bash
curl -X POST http://localhost:8081 \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": "1",
    "method": "initialize",
    "params": {
      "protocolVersion": "2024-11-05",
      "capabilities": {
        "experimental": {},
        "sampling": {}
      },
      "clientInfo": {
        "name": "test-client",
        "version": "1.0.0"
      }
    }
  }'
```

### List Available Tools

```bash
curl -X POST http://localhost:8081 \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": "2",
    "method": "tools/list",
    "params": {}
  }'
```

### Call a Tool (Calculator)

```bash
curl -X POST http://localhost:8081 \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": "3",
    "method": "tools/call",
    "params": {
      "name": "calculator",
      "arguments": {
        "expression": "15 * 23 + 7"
      }
    }
  }'
```

### List Resources

```bash
curl -X POST http://localhost:8081 \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": "4",
    "method": "resources/list",
    "params": {}
  }'
```

### Read Context Resource

```bash
curl -X POST http://localhost:8081 \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": "5",
    "method": "resources/read",
    "params": {
      "uri": "context://user123"
    }
  }'
```

### Completion Request

```bash
curl -X POST http://localhost:8081 \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": "6",
    "method": "completion",
    "params": {
      "model": "claude-3-5-sonnet-20241022",
      "messages": [
        {
          "role": "system",
          "content": {
            "type": "text",
            "text": "You are a helpful AI assistant."
          }
        },
        {
          "role": "user",
          "content": {
            "type": "text", 
            "text": "Explain machine learning in simple terms."
          }
        }
      ]
    }
  }'
```

### List Prompts

```bash
curl -X POST http://localhost:8081 \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": "7",
    "method": "prompts/list",
    "params": {}
  }'
```

### Get Prompt

```bash
curl -X POST http://localhost:8081 \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": "8",
    "method": "prompts/get",
    "params": {
      "name": "summarize",
      "arguments": {
        "text": "This is a long text that needs summarization...",
        "style": "brief"
      }
    }
  }'
```

## Available Models

### OpenAI Models
- `gpt-5` - GPT-5 (latest)
- `gpt-4o` - GPT-4 Omni
- `gpt-4.1` - GPT-4.1 Turbo
- `gpt-4.1-mini` - GPT-4.1 Mini
- `gpt-3.5` - GPT-3.5 Turbo

### Anthropic Models
- `claude-3-5-sonnet-20241022` - Claude 3.5 Sonnet
- `claude-3-5-haiku-20241022` - Claude 3.5 Haiku
- `claude-3-opus-latest` - Claude 3 Opus
- `claude-3-sonnet-20240229` - Claude 3 Sonnet
- `claude-3-haiku-20240307` - Claude 3 Haiku
- `claude-opus-4-1-20250805` - Claude Opus 4.1

### Local Models
- `llama2` - Llama 2
- `mistral` - Mistral

## Built-in Tools

### Calculator Tool
Performs mathematical calculations.

```json
{
  "name": "calculator",
  "arguments": {
    "expression": "2 + 2 * 3"
  }
}
```

### File System Tool
File operations (read, write, list).

```json
{
  "name": "filesystem",
  "arguments": {
    "operation": "read",
    "path": "/path/to/file.txt"
  }
}
```

### Memory Tool
Long-term memory operations.

```json
{
  "name": "memory",
  "arguments": {
    "operation": "store",
    "key": "user_preference",
    "value": "prefers detailed explanations"
  }
}
```

## Error Handling

The framework returns structured error responses:

### REST API Errors
```json
{
  "error": "Model not found: invalid-model",
  "timestamp": "2024-01-15T10:30:00Z",
  "status": 400
}
```

### MCP Protocol Errors
```json
{
  "jsonrpc": "2.0",
  "id": "1",
  "error": {
    "code": -32602,
    "message": "Invalid params",
    "data": "Missing required parameter: modelName"
  }
}
```

## Development

### Running Tests
```bash
mvn test
```

### Building Docker Image
```bash
docker build -t agentic-framework .
docker run -p 8080:8080 -p 8081:8081 agentic-framework
```

### Adding New Models
1. Implement `ModelProvider` interface
2. Register in `ModelRegistry`
3. Add configuration in `application.yml`

### Adding New Tools
1. Implement `McpTool` interface
2. Register in `ToolRegistry`
3. Define JSON schema for arguments

## Configuration Options

| Property | Default | Description |
|----------|---------|-------------|
| `server.port` | 8080 | REST API port |
| `mcp.server.port` | 8081 | MCP server port |
| `context.storage.max-contexts-per-user` | 100 | Max contexts per user |
| `context.memory.max-memories-per-user` | 1000 | Max memories per user |
| `context.storage.cleanup-interval` | 3600000 | Cleanup interval (ms) |

## Troubleshooting

### Common Issues

1. **Port conflicts**: Change ports in `application.yml`
2. **API key errors**: Verify keys in configuration
3. **Memory issues**: Adjust JVM heap size with `-Xmx4g`

### Logs
- Application logs: `logs/application.log`
- Access logs: `logs/access.log`
- Error logs: `logs/error.log`

## License

MIT License - see LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request