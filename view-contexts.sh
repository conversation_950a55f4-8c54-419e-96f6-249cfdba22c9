#!/bin/bash

# Script to view user contexts in MCP server

BASE_URL="http://localhost:8080/api/v1"

echo "MCP Context Viewer"
echo "=================="
echo ""
echo "1. View specific user context"
echo "2. Search user memories"
echo "3. View server metrics"
echo "4. View all available models"
echo ""
read -p "Select option (1-4): " option

case $option in
    1)
        read -p "Enter user ID: " userId
        read -p "Enter limit (default 10): " limit
        limit=${limit:-10}
        echo ""
        echo "Fetching context for user: $userId"
        curl -s "$BASE_URL/context/$userId?limit=$limit" | python3 -m json.tool
        ;;
    2)
        read -p "Enter user ID: " userId
        read -p "Enter search query: " query
        read -p "Enter limit (default 10): " limit
        limit=${limit:-10}
        echo ""
        echo "Searching memories for user: $userId"
        curl -s "$BASE_URL/context/$userId/memories?query=$query&limit=$limit" | python3 -m json.tool
        ;;
    3)
        echo ""
        echo "Server Metrics:"
        curl -s "$BASE_URL/metrics" | python3 -m json.tool
        ;;
    4)
        echo ""
        echo "Available Models:"
        curl -s "$BASE_URL/models" | python3 -m json.tool
        ;;
    *)
        echo "Invalid option"
        ;;
esac